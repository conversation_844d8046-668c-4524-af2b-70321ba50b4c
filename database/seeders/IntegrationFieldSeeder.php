<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\IntegrationField;

/**
 * IntegrationFieldSeeder
 * 
 * Seeds the integration_fields table with default field mappings
 * for the fictional platform data mapping system.
 */
class IntegrationFieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing data
        IntegrationField::truncate();

        // Default field mappings for fictional platforms
        $mappings = [
            // Platform A mappings
            [
                'provider_name' => 'platform_a',
                'local_field_name' => 'name',
                'provider_field_name' => 'full_name',
                'field_type' => 'string',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform A full_name field to standardized name field',
            ],
            [
                'provider_name' => 'platform_a',
                'local_field_name' => 'age',
                'provider_field_name' => 'user_age',
                'field_type' => 'integer',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform A user_age field to standardized age field',
            ],
            [
                'provider_name' => 'platform_a',
                'local_field_name' => 'email',
                'provider_field_name' => 'email_address',
                'field_type' => 'string',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform A email_address field to standardized email field',
                'validation_rules' => json_encode(['email']),
            ],

            // Platform B mappings
            [
                'provider_name' => 'platform_b',
                'local_field_name' => 'name',
                'provider_field_name' => 'display_name',
                'field_type' => 'string',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform B display_name field to standardized name field',
            ],
            [
                'provider_name' => 'platform_b',
                'local_field_name' => 'age',
                'provider_field_name' => 'years_old',
                'field_type' => 'integer',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform B years_old field to standardized age field',
            ],
            [
                'provider_name' => 'platform_b',
                'local_field_name' => 'email',
                'provider_field_name' => 'contact_email',
                'field_type' => 'string',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform B contact_email field to standardized email field',
                'validation_rules' => json_encode(['email']),
            ],

            // Platform C mappings
            [
                'provider_name' => 'platform_c',
                'local_field_name' => 'name',
                'provider_field_name' => 'person_name',
                'field_type' => 'string',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform C person_name field to standardized name field',
            ],
            [
                'provider_name' => 'platform_c',
                'local_field_name' => 'age',
                'provider_field_name' => 'age_in_years',
                'field_type' => 'integer',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform C age_in_years field to standardized age field',
            ],
            [
                'provider_name' => 'platform_c',
                'local_field_name' => 'email',
                'provider_field_name' => 'email_addr',
                'field_type' => 'string',
                'is_required' => true,
                'is_active' => true,
                'description' => 'Maps Platform C email_addr field to standardized email field',
                'validation_rules' => json_encode(['email']),
            ],
        ];

        // Insert all mappings
        foreach ($mappings as $mapping) {
            IntegrationField::create($mapping);
        }

        $this->command->info('Created ' . count($mappings) . ' integration field mappings.');
    }
}
