CREATE TABLE IF NOT EXISTS "migrations"(
  "id" integer primary key autoincrement not null,
  "migration" varchar not null,
  "batch" integer not null
);
CREATE TABLE IF NOT EXISTS "users"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "email" varchar not null,
  "email_verified_at" datetime,
  "password" varchar not null,
  "remember_token" varchar,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE UNIQUE INDEX "users_email_unique" on "users"("email");
CREATE TABLE IF NOT EXISTS "password_reset_tokens"(
  "email" varchar not null,
  "token" varchar not null,
  "created_at" datetime,
  primary key("email")
);
CREATE TABLE IF NOT EXISTS "sessions"(
  "id" varchar not null,
  "user_id" integer,
  "ip_address" varchar,
  "user_agent" text,
  "payload" text not null,
  "last_activity" integer not null,
  primary key("id")
);
CREATE INDEX "sessions_user_id_index" on "sessions"("user_id");
CREATE INDEX "sessions_last_activity_index" on "sessions"("last_activity");
CREATE TABLE IF NOT EXISTS "cache"(
  "key" varchar not null,
  "value" text not null,
  "expiration" integer not null,
  primary key("key")
);
CREATE TABLE IF NOT EXISTS "cache_locks"(
  "key" varchar not null,
  "owner" varchar not null,
  "expiration" integer not null,
  primary key("key")
);
CREATE TABLE IF NOT EXISTS "jobs"(
  "id" integer primary key autoincrement not null,
  "queue" varchar not null,
  "payload" text not null,
  "attempts" integer not null,
  "reserved_at" integer,
  "available_at" integer not null,
  "created_at" integer not null
);
CREATE INDEX "jobs_queue_index" on "jobs"("queue");
CREATE TABLE IF NOT EXISTS "job_batches"(
  "id" varchar not null,
  "name" varchar not null,
  "total_jobs" integer not null,
  "pending_jobs" integer not null,
  "failed_jobs" integer not null,
  "failed_job_ids" text not null,
  "options" text,
  "cancelled_at" integer,
  "created_at" integer not null,
  "finished_at" integer,
  primary key("id")
);
CREATE TABLE IF NOT EXISTS "failed_jobs"(
  "id" integer primary key autoincrement not null,
  "uuid" varchar not null,
  "connection" text not null,
  "queue" text not null,
  "payload" text not null,
  "exception" text not null,
  "failed_at" datetime not null default CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX "failed_jobs_uuid_unique" on "failed_jobs"("uuid");
CREATE TABLE IF NOT EXISTS "payment_transactions"(
  "id" integer primary key autoincrement not null,
  "provider" varchar not null,
  "provider_transaction_id" varchar not null,
  "provider_payment_intent_id" varchar,
  "type" varchar check("type" in('payment', 'refund', 'partial_refund', 'chargeback', 'subscription_payment', 'subscription_cancel')) not null,
  "status" varchar check("status" in('pending', 'succeeded', 'failed', 'canceled', 'requires_action')) not null,
  "amount" numeric not null,
  "currency" varchar not null,
  "fee" numeric,
  "net_amount" numeric,
  "customer_id" varchar,
  "customer_email" varchar,
  "customer_details" text,
  "payment_method_type" varchar,
  "payment_method_details" text,
  "metadata" text,
  "description" text,
  "processed_at" datetime,
  "idempotency_key" varchar,
  "raw_provider_data" text not null,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE INDEX "payment_transactions_provider_status_index" on "payment_transactions"(
  "provider",
  "status"
);
CREATE INDEX "payment_transactions_provider_type_index" on "payment_transactions"(
  "provider",
  "type"
);
CREATE INDEX "payment_transactions_customer_email_status_index" on "payment_transactions"(
  "customer_email",
  "status"
);
CREATE INDEX "payment_transactions_processed_at_status_index" on "payment_transactions"(
  "processed_at",
  "status"
);
CREATE INDEX "payment_transactions_provider_index" on "payment_transactions"(
  "provider"
);
CREATE INDEX "payment_transactions_provider_transaction_id_index" on "payment_transactions"(
  "provider_transaction_id"
);
CREATE INDEX "payment_transactions_provider_payment_intent_id_index" on "payment_transactions"(
  "provider_payment_intent_id"
);
CREATE INDEX "payment_transactions_type_index" on "payment_transactions"(
  "type"
);
CREATE INDEX "payment_transactions_status_index" on "payment_transactions"(
  "status"
);
CREATE INDEX "payment_transactions_customer_id_index" on "payment_transactions"(
  "customer_id"
);
CREATE INDEX "payment_transactions_customer_email_index" on "payment_transactions"(
  "customer_email"
);
CREATE INDEX "payment_transactions_processed_at_index" on "payment_transactions"(
  "processed_at"
);
CREATE UNIQUE INDEX "payment_transactions_idempotency_key_unique" on "payment_transactions"(
  "idempotency_key"
);
CREATE TABLE IF NOT EXISTS "webhook_events"(
  "id" integer primary key autoincrement not null,
  "provider" varchar not null,
  "provider_event_id" varchar not null,
  "event_type" varchar not null,
  "webhook_endpoint" varchar not null,
  "status" varchar check("status" in('pending', 'processing', 'processed', 'failed', 'retrying')) not null default 'pending',
  "retry_count" integer not null default '0',
  "last_retry_at" datetime,
  "processed_at" datetime,
  "request_method" varchar not null default 'POST',
  "request_headers" text not null,
  "request_body" text not null,
  "request_ip" varchar,
  "user_agent" varchar,
  "signature_verified" tinyint(1) not null default '0',
  "signature_header" varchar,
  "verification_error" text,
  "processing_error" text,
  "processing_result" text,
  "payment_transaction_id" integer,
  "idempotency_key" varchar,
  "is_duplicate" tinyint(1) not null default '0',
  "original_event_id" integer,
  "metadata" text,
  "processing_time_ms" numeric,
  "created_at" datetime,
  "updated_at" datetime,
  foreign key("payment_transaction_id") references "payment_transactions"("id") on delete set null,
  foreign key("original_event_id") references "webhook_events"("id") on delete set null
);
CREATE INDEX "webhook_events_provider_status_index" on "webhook_events"(
  "provider",
  "status"
);
CREATE INDEX "webhook_events_provider_event_type_index" on "webhook_events"(
  "provider",
  "event_type"
);
CREATE INDEX "webhook_events_status_retry_count_index" on "webhook_events"(
  "status",
  "retry_count"
);
CREATE INDEX "webhook_events_created_at_status_index" on "webhook_events"(
  "created_at",
  "status"
);
CREATE INDEX "webhook_events_provider_event_id_provider_index" on "webhook_events"(
  "provider_event_id",
  "provider"
);
CREATE INDEX "webhook_events_provider_index" on "webhook_events"("provider");
CREATE INDEX "webhook_events_provider_event_id_index" on "webhook_events"(
  "provider_event_id"
);
CREATE INDEX "webhook_events_event_type_index" on "webhook_events"(
  "event_type"
);
CREATE INDEX "webhook_events_webhook_endpoint_index" on "webhook_events"(
  "webhook_endpoint"
);
CREATE INDEX "webhook_events_status_index" on "webhook_events"("status");
CREATE INDEX "webhook_events_processed_at_index" on "webhook_events"(
  "processed_at"
);
CREATE INDEX "webhook_events_signature_verified_index" on "webhook_events"(
  "signature_verified"
);
CREATE INDEX "webhook_events_payment_transaction_id_index" on "webhook_events"(
  "payment_transaction_id"
);
CREATE UNIQUE INDEX "webhook_events_idempotency_key_unique" on "webhook_events"(
  "idempotency_key"
);
CREATE INDEX "webhook_events_is_duplicate_index" on "webhook_events"(
  "is_duplicate"
);
CREATE INDEX "webhook_events_original_event_id_index" on "webhook_events"(
  "original_event_id"
);
CREATE TABLE IF NOT EXISTS "payment_providers"(
  "id" integer primary key autoincrement not null,
  "name" varchar not null,
  "display_name" varchar not null,
  "description" text,
  "is_active" tinyint(1) not null default '1',
  "is_test_mode" tinyint(1) not null default '0',
  "webhook_endpoints" text not null,
  "supported_events" text not null,
  "webhook_secret" text,
  "api_key" text,
  "additional_credentials" text,
  "max_retry_attempts" integer not null default '3',
  "retry_delay_seconds" integer not null default '60',
  "signature_verification_required" tinyint(1) not null default '1',
  "signature_verification_method" varchar not null,
  "rate_limit_per_minute" integer,
  "rate_limit_per_hour" integer,
  "last_webhook_received_at" datetime,
  "total_webhooks_received" integer not null default '0',
  "failed_webhooks_count" integer not null default '0',
  "success_rate_percentage" numeric,
  "provider_settings" text,
  "created_at" datetime,
  "updated_at" datetime
);
CREATE INDEX "payment_providers_is_active_is_test_mode_index" on "payment_providers"(
  "is_active",
  "is_test_mode"
);
CREATE INDEX "payment_providers_last_webhook_received_at_index" on "payment_providers"(
  "last_webhook_received_at"
);
CREATE UNIQUE INDEX "payment_providers_name_unique" on "payment_providers"(
  "name"
);
CREATE INDEX "payment_providers_is_active_index" on "payment_providers"(
  "is_active"
);
CREATE INDEX "payment_providers_is_test_mode_index" on "payment_providers"(
  "is_test_mode"
);

INSERT INTO migrations VALUES(1,'0001_01_01_000000_create_users_table',1);
INSERT INTO migrations VALUES(2,'0001_01_01_000001_create_cache_table',1);
INSERT INTO migrations VALUES(3,'0001_01_01_000002_create_jobs_table',1);
INSERT INTO migrations VALUES(4,'2025_09_02_211144_create_payment_transactions_table',2);
INSERT INTO migrations VALUES(5,'2025_09_02_211212_create_webhook_events_table',2);
INSERT INTO migrations VALUES(6,'2025_09_02_211242_create_payment_providers_table',2);
