<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_events', function (Blueprint $table) {
            $table->id();

            // Provider and event identification
            $table->string('provider')->index(); // stripe, paypal, etc.
            $table->string('provider_event_id')->index(); // Provider's unique event ID
            $table->string('event_type')->index(); // payment.succeeded, invoice.paid, etc.
            $table->string('webhook_endpoint')->index(); // Which endpoint received this

            // Processing status
            $table->enum('status', ['pending', 'processing', 'processed', 'failed', 'retrying'])->default('pending')->index();
            $table->integer('retry_count')->default(0);
            $table->timestamp('last_retry_at')->nullable();
            $table->timestamp('processed_at')->nullable()->index();

            // Request details
            $table->string('request_method', 10)->default('POST');
            $table->json('request_headers'); // HTTP headers from webhook request
            $table->longText('request_body'); // Raw webhook payload
            $table->string('request_ip')->nullable();
            $table->string('user_agent')->nullable();

            // Signature verification
            $table->boolean('signature_verified')->default(false)->index();
            $table->string('signature_header')->nullable(); // The signature header value
            $table->text('verification_error')->nullable(); // Error if verification failed

            // Processing results
            $table->text('processing_error')->nullable(); // Error message if processing failed
            $table->json('processing_result')->nullable(); // Result data from processing
            $table->unsignedBigInteger('payment_transaction_id')->nullable()->index(); // Link to created transaction

            // Idempotency and duplicate detection
            $table->string('idempotency_key')->nullable()->unique();
            $table->boolean('is_duplicate')->default(false)->index();
            $table->unsignedBigInteger('original_event_id')->nullable()->index(); // If duplicate, link to original

            // Metadata
            $table->json('metadata')->nullable(); // Additional processing metadata
            $table->decimal('processing_time_ms', 8, 2)->nullable(); // Processing time in milliseconds

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('payment_transaction_id')->references('id')->on('payment_transactions')->onDelete('set null');
            $table->foreign('original_event_id')->references('id')->on('webhook_events')->onDelete('set null');

            // Indexes for common queries
            $table->index(['provider', 'status']);
            $table->index(['provider', 'event_type']);
            $table->index(['status', 'retry_count']);
            $table->index(['created_at', 'status']);
            $table->index(['provider_event_id', 'provider']); // Composite for uniqueness checks
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_events');
    }
};
