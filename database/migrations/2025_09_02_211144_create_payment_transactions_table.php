<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();

            // Provider identification
            $table->string('provider')->index(); // stripe, paypal, etc.
            $table->string('provider_transaction_id')->index(); // Provider's unique transaction ID
            $table->string('provider_payment_intent_id')->nullable()->index(); // Provider's payment intent ID

            // Transaction details
            $table->enum('type', ['payment', 'refund', 'partial_refund', 'chargeback', 'subscription_payment', 'subscription_cancel'])->index();
            $table->enum('status', ['pending', 'succeeded', 'failed', 'canceled', 'requires_action'])->index();
            $table->decimal('amount', 10, 2); // Amount in smallest currency unit (cents)
            $table->string('currency', 3); // ISO 4217 currency code
            $table->decimal('fee', 10, 2)->nullable(); // Provider fee
            $table->decimal('net_amount', 10, 2)->nullable(); // Amount after fees

            // Customer information
            $table->string('customer_id')->nullable()->index(); // Provider's customer ID
            $table->string('customer_email')->nullable()->index();
            $table->json('customer_details')->nullable(); // Additional customer info

            // Payment method
            $table->string('payment_method_type')->nullable(); // card, bank_transfer, etc.
            $table->json('payment_method_details')->nullable(); // Card last 4, bank details, etc.

            // Metadata and tracking
            $table->json('metadata')->nullable(); // Custom metadata from provider
            $table->text('description')->nullable();
            $table->timestamp('processed_at')->nullable()->index();
            $table->string('idempotency_key')->nullable()->unique(); // For duplicate prevention

            // Raw provider data
            $table->json('raw_provider_data'); // Complete original webhook payload

            $table->timestamps();

            // Indexes for common queries
            $table->index(['provider', 'status']);
            $table->index(['provider', 'type']);
            $table->index(['customer_email', 'status']);
            $table->index(['processed_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
