<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('integration_fields', function (Blueprint $table) {
            $table->id();
            
            // Core field mapping columns
            $table->string('provider_name', 100)->comment('Name of the integration provider (e.g., platform_a, platform_b)');
            $table->string('local_field_name', 100)->comment('Standardized local field name (e.g., name, age, email)');
            $table->string('provider_field_name', 100)->comment('Provider-specific field name (e.g., full_name, user_age)');
            
            // Optional configuration columns
            $table->string('field_type', 50)->default('string')->comment('Data type of the field (string, integer, boolean, etc.)');
            $table->boolean('is_required')->default(false)->comment('Whether this field mapping is required for the provider');
            $table->boolean('is_active')->default(true)->comment('Whether this field mapping is currently active');
            $table->text('transformation_rules')->nullable()->comment('JSON rules for data transformation (optional)');
            $table->text('validation_rules')->nullable()->comment('JSON validation rules for the field (optional)');
            $table->text('description')->nullable()->comment('Human-readable description of the field mapping');
            
            // Metadata columns
            $table->json('metadata')->nullable()->comment('Additional metadata for the field mapping');
            $table->timestamps();
            
            // Indexes for performance optimization
            $table->index(['provider_name'], 'idx_integration_fields_provider');
            $table->index(['local_field_name'], 'idx_integration_fields_local');
            $table->index(['provider_name', 'local_field_name'], 'idx_integration_fields_provider_local');
            $table->index(['is_active'], 'idx_integration_fields_active');
            
            // Unique constraint to prevent duplicate mappings
            $table->unique(['provider_name', 'local_field_name'], 'uk_integration_fields_provider_local');
            
            // Optional: Add foreign key constraint if you have a providers table
            // $table->foreign('provider_name')->references('name')->on('providers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('integration_fields');
    }
};
