<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_providers', function (Blueprint $table) {
            $table->id();

            // Provider identification
            $table->string('name')->unique(); // stripe, paypal, square, etc.
            $table->string('display_name'); // Human-readable name
            $table->text('description')->nullable();

            // Configuration
            $table->boolean('is_active')->default(true)->index();
            $table->boolean('is_test_mode')->default(false)->index();
            $table->json('webhook_endpoints'); // Array of webhook endpoint URLs
            $table->json('supported_events'); // Array of supported event types

            // Credentials (encrypted)
            $table->text('webhook_secret')->nullable(); // Encrypted webhook signing secret
            $table->text('api_key')->nullable(); // Encrypted API key if needed for verification
            $table->json('additional_credentials')->nullable(); // Other encrypted credentials

            // Processing configuration
            $table->integer('max_retry_attempts')->default(3);
            $table->integer('retry_delay_seconds')->default(60);
            $table->boolean('signature_verification_required')->default(true);
            $table->string('signature_verification_method'); // hmac_sha256, rsa, api_call, etc.

            // Rate limiting
            $table->integer('rate_limit_per_minute')->nullable();
            $table->integer('rate_limit_per_hour')->nullable();

            // Monitoring
            $table->timestamp('last_webhook_received_at')->nullable();
            $table->integer('total_webhooks_received')->default(0);
            $table->integer('failed_webhooks_count')->default(0);
            $table->decimal('success_rate_percentage', 5, 2)->nullable();

            // Provider-specific settings
            $table->json('provider_settings')->nullable(); // Custom settings per provider

            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'is_test_mode']);
            $table->index('last_webhook_received_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_providers');
    }
};
