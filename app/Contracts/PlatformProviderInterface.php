<?php

namespace App\Contracts;

use App\DTOs\UserProfileData;

/**
 * Interface for fictional platform provider implementations
 *
 * This interface defines the contract that all fictional platform provider classes
 * must implement to handle person profile data parsing and mapping to standardized format.
 */
interface PlatformProviderInterface
{
    /**
     * Get the provider name (e.g., 'platform_a', 'platform_b', 'platform_c')
     */
    public function getName(): string;

    /**
     * Get the provider display name (e.g., 'Platform A', 'Platform B', 'Platform C')
     */
    public function getDisplayName(): string;

    /**
     * Parse person profile data into standardized format using dynamic field mappings
     *
     * @param string $payload Raw person profile data (JSON string from API response)
     * @param array $headers Optional headers for context
     * @return UserProfileData
     */
    public function parsePersonProfileData(string $payload, array $headers = []): UserProfileData;

    /**
     * Map person profile data to standardized database format
     *
     * @param UserProfileData $profileData
     * @return array Database-ready array
     */
    public function mapToStandardFormat(UserProfileData $profileData): array;
}
