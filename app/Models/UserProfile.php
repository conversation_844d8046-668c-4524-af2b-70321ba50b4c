<?php

namespace App\Models;

/**
 * UserProfile Data Class
 *
 * Simple data structure for holding standardized person profile information
 * from various fictional platforms.
 */
class UserProfile
{
    public function __construct(
        public string $provider,
        public string $name,
        public int $age,
        public string $email,
        public ?array $raw_provider_data = null,
    ) {
    }

    /**
     * Create a new UserProfile from array data
     */
    public static function fromArray(array $data): self
    {
        return new self(
            provider: $data['provider'],
            name: $data['name'],
            age: (int) $data['age'],
            email: $data['email'],
            raw_provider_data: $data['raw_provider_data'] ?? null,
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'provider' => $this->provider,
            'name' => $this->name,
            'age' => $this->age,
            'email' => $this->email,
            'raw_provider_data' => $this->raw_provider_data,
        ];
    }

    /**
     * Check if profile has valid email
     */
    public function hasValidEmail(): bool
    {
        return !empty($this->email) && filter_var($this->email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Check if person is adult (18 or older)
     */
    public function isAdult(): bool
    {
        return $this->age >= 18;
    }

    /**
     * Get age category
     */
    public function getAgeCategory(): string
    {
        if ($this->age < 18) {
            return 'minor';
        } elseif ($this->age < 65) {
            return 'adult';
        } else {
            return 'senior';
        }
    }

    /**
     * Get display summary
     */
    public function getDisplaySummary(): string
    {
        return "{$this->name} ({$this->age} years old) - {$this->email}";
    }
}
