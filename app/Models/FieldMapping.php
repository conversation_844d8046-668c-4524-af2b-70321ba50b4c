<?php

namespace App\Models;

/**
 * FieldMapping Data Class
 *
 * Simple data structure for holding field mappings that map
 * provider-specific field names to standardized local field names.
 */
class FieldMapping
{
    public function __construct(
        public string $provider_name,
        public string $local_field_name,
        public string $provider_field_name,
    ) {
    }

    /**
     * Create a new FieldMapping from array data
     */
    public static function fromArray(array $data): self
    {
        return new self(
            provider_name: $data['provider_name'],
            local_field_name: $data['local_field_name'],
            provider_field_name: $data['provider_field_name'],
        );
    }

    /**
     * Convert to array representation
     */
    public function toArray(): array
    {
        return [
            'provider_name' => $this->provider_name,
            'local_field_name' => $this->local_field_name,
            'provider_field_name' => $this->provider_field_name,
        ];
    }

    /**
     * Check if this mapping is for a specific provider
     */
    public function isForProvider(string $providerName): bool
    {
        return $this->provider_name === $providerName;
    }

    /**
     * Check if this mapping is for a specific local field
     */
    public function isForLocalField(string $localFieldName): bool
    {
        return $this->local_field_name === $localFieldName;
    }
}
