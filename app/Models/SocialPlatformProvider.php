<?php

namespace App\Models;

/**
 * SocialPlatformProvider Data Class
 *
 * Simple data structure for holding platform provider configuration
 * for the dynamic field mapping system.
 */
class SocialPlatformProvider
{
    public function __construct(
        public string $name,
        public string $display_name,
        public bool $is_active = true,
        public array $supported_data_types = [],
        public array $provider_settings = [],
    ) {
    }

    /**
     * Check if provider is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if provider supports a data type
     */
    public function supportsDataType(string $dataType): bool
    {
        return in_array($dataType, $this->supported_data_types, true);
    }

    /**
     * Get provider configuration setting
     */
    public function getSetting(string $key, $default = null)
    {
        return $this->provider_settings[$key] ?? $default;
    }

    /**
     * Set provider configuration setting
     */
    public function setSetting(string $key, $value): void
    {
        $this->provider_settings[$key] = $value;
    }
}
