<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * IntegrationField Model
 *
 * Database-driven field mappings between provider-specific field names
 * and standardized local field names for dynamic platform integration.
 */
class IntegrationField extends Model
{
    protected $fillable = [
        'provider_name',
        'local_field_name',
        'provider_field_name',
        'field_type',
        'is_required',
        'is_active',
        'description',
        'metadata',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];
}
