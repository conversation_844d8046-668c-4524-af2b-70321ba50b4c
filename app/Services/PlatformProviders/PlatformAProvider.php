<?php

namespace App\Services\PlatformProviders;

use App\DTOs\UserProfileData;

/**
 * Platform A provider
 *
 * Handles parsing and mapping of Platform A person profile data to standardized format.
 * Platform A uses field names: full_name, user_age, email_address
 */
class PlatformAProvider extends AbstractPlatformProvider
{
    /**
     * {@inheritdoc}
     */
    public function parsePersonProfileData(string $payload, array $headers = []): UserProfileData
    {
        $data = json_decode($payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON payload');
        }

        // Use dynamic field mapping to transform provider data to standardized format
        return $this->mapProviderDataToStandardFormat($data);
    }

    /**
     * {@inheritdoc}
     */
    public function mapToStandardFormat(UserProfileData $profileData): array
    {
        return $profileData->toDatabaseArray();
    }
}
