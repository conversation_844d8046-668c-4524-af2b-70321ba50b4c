<?php

namespace App\Services\PlatformProviders;

use App\DTOs\UserProfileData;

/**
 * Platform C provider
 *
 * Handles parsing and mapping of Platform C person profile data to standardized format.
 * Platform C uses field names: person_name, age_in_years, email_addr
 */
class PlatformCProvider extends AbstractPlatformProvider
{
    /**
     * {@inheritdoc}
     */
    public function parsePersonProfileData(string $payload, array $headers = []): UserProfileData
    {
        $data = json_decode($payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON payload');
        }

        // Use dynamic field mapping to transform provider data to standardized format
        return $this->mapProviderDataToStandardFormat($data);
    }

    /**
     * {@inheritdoc}
     */
    public function mapToStandardFormat(UserProfileData $profileData): array
    {
        return $profileData->toDatabaseArray();
    }
}
