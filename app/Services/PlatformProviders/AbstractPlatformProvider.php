<?php

namespace App\Services\PlatformProviders;

use App\Contracts\PlatformProviderInterface;
use App\DTOs\UserProfileData;
use App\Models\SocialPlatformProvider;
use App\Services\FieldMappingRepository;

/**
 * Abstract base class for fictional platform provider implementations
 *
 * Provides common functionality for all platform providers including:
 * - Basic provider information access
 * - Database-driven field mapping support
 * - Data type validation
 */
abstract class AbstractPlatformProvider implements PlatformProviderInterface
{
    protected SocialPlatformProvider $config;
    protected FieldMappingRepository $fieldMappingRepository;

    public function __construct(SocialPlatformProvider $config, FieldMappingRepository $fieldMappingRepository)
    {
        $this->config = $config;
        $this->fieldMappingRepository = $fieldMappingRepository;
    }

    /**
     * Get the provider name
     */
    public function getName(): string
    {
        return $this->config->name;
    }

    /**
     * Get the provider display name
     */
    public function getDisplayName(): string
    {
        return $this->config->display_name;
    }

    /**
     * Map provider data to standardized format using database-driven field mappings
     *
     * This method uses the FieldMappingRepository to dynamically map provider-specific
     * field names to standardized local field names (name, age, email).
     *
     * @param array $providerData Raw data from provider API
     * @return UserProfileData Standardized user profile data
     */
    protected function mapProviderDataToStandardFormat(array $providerData): UserProfileData
    {
        $mappings = $this->fieldMappingRepository->getMappingsForProvider($this->getName());

        $name = $providerData[$mappings['name']] ?? '';
        $age = (int) ($providerData[$mappings['age']] ?? 0);
        $email = $providerData[$mappings['email']] ?? '';

        return new UserProfileData(
            provider: $this->getName(),
            name: $name,
            age: $age,
            email: $email,
            rawData: $providerData
        );
    }

    /**
     * Parse person profile data into standardized format using dynamic field mappings
     *
     * @param string $payload Raw person profile data (JSON string from API response)
     * @param array $headers Optional headers for context
     * @return UserProfileData Standardized user profile data
     */
    abstract public function parsePersonProfileData(string $payload, array $headers = []): UserProfileData;

    /**
     * Map person profile data to standardized database format
     *
     * @param UserProfileData $profileData Standardized profile data
     * @return array Database-ready array
     */
    abstract public function mapToStandardFormat(UserProfileData $profileData): array;
}
