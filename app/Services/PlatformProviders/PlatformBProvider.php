<?php

namespace App\Services\PlatformProviders;

use App\DTOs\UserProfileData;

/**
 * Platform B provider
 *
 * Handles parsing and mapping of Platform B person profile data to standardized format.
 * Platform B uses field names: display_name, years_old, contact_email
 */
class PlatformBProvider extends AbstractPlatformProvider
{
    /**
     * {@inheritdoc}
     */
    public function parsePersonProfileData(string $payload, array $headers = []): UserProfileData
    {
        $data = json_decode($payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON payload');
        }

        // Use dynamic field mapping to transform provider data to standardized format
        return $this->mapProviderDataToStandardFormat($data);
    }

    /**
     * {@inheritdoc}
     */
    public function mapToStandardFormat(UserProfileData $profileData): array
    {
        return $profileData->toDatabaseArray();
    }
}
