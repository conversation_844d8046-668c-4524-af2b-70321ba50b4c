<?php

namespace App\Services;

use App\Models\FieldMapping;
use App\Models\IntegrationField;

/**
 * FieldMappingRepository
 *
 * Repository for managing field mappings between provider-specific field names
 * and standardized local field names. Uses static data instead of database.
 */
class FieldMappingRepository
{
    protected array $mappings = [];

    public function __construct()
    {
        $this->loadDefaultMappings();
    }

    /**
     * Get field mappings for a specific provider
     *
     * @param string $providerName
     * @return array<string, string> Array where key is local field name and value is provider field name
     */
    public function getMappingsForProvider(string $providerName): array
    {
        $providerMappings = [];

        foreach ($this->mappings as $mapping) {
            if ($mapping->isForProvider($providerName)) {
                $providerMappings[$mapping->local_field_name] = $mapping->provider_field_name;
            }
        }

        return $providerMappings;
    }

    /**
     * Get provider field name for a local field name
     *
     * @param string $providerName
     * @param string $localFieldName
     * @return string|null
     */
    public function getProviderFieldName(string $providerName, string $localFieldName): ?string
    {
        foreach ($this->mappings as $mapping) {
            if ($mapping->isForProvider($providerName) && $mapping->isForLocalField($localFieldName)) {
                return $mapping->provider_field_name;
            }
        }

        return null;
    }

    /**
     * Add a field mapping
     *
     * @param FieldMapping $mapping
     */
    public function addMapping(FieldMapping $mapping): void
    {
        $this->mappings[] = $mapping;
    }

    /**
     * Get all mappings
     *
     * @return FieldMapping[]
     */
    public function getAllMappings(): array
    {
        return $this->mappings;
    }

    /**
     * Load default field mappings for fictional platforms
     */
    protected function loadDefaultMappings(): void
    {
        // Load from database first
        IntegrationField::query()->each(function (IntegrationField $field) {
            $this->mappings[] = new FieldMapping(
                provider_name: $field->provider_name,
                local_field_name: $field->local_field_name,
                provider_field_name: $field->provider_field_name,
            );
        });

        // Add static fallback mappings if no database mappings exist
        if (empty($this->mappings)) {
            $this->loadStaticMappings();
        }
    }

    /**
     * Load static fallback mappings
     */
    protected function loadStaticMappings(): void
    {
        $staticMappings = [
            // Platform A mappings
            ['platform_a', 'name', 'full_name'],
            ['platform_a', 'age', 'user_age'],
            ['platform_a', 'email', 'email_address'],

            // Platform B mappings
            ['platform_b', 'name', 'display_name'],
            ['platform_b', 'age', 'years_old'],
            ['platform_b', 'email', 'contact_email'],

            // Platform C mappings
            ['platform_c', 'name', 'person_name'],
            ['platform_c', 'age', 'age_in_years'],
            ['platform_c', 'email', 'email_addr'],
        ];

        foreach ($staticMappings as [$provider, $local, $remote]) {
            $this->mappings[] = new FieldMapping(
                provider_name: $provider,
                local_field_name: $local,
                provider_field_name: $remote,
            );
        }
    }
}
