<?php

namespace App\Services;

use App\Contracts\PlatformProviderInterface;
use App\Models\SocialPlatformProvider;
use App\Services\FieldMappingRepository;

/**
 * Registry for managing fictional platform provider instances
 *
 * Simplified version focused only on provider instantiation for data mapping.
 * Works with static SocialPlatformProvider data classes and field mappings.
 */
class PlatformProviderRegistry
{
    protected array $providers = [];
    protected array $providerClasses = [];
    protected array $providerConfigs = [];
    protected FieldMappingRepository $fieldMappingRepository;

    public function __construct()
    {
        $this->fieldMappingRepository = new FieldMappingRepository();
        $this->loadProviderClasses();
        $this->loadDefaultConfigurations();
    }

    /**
     * Register a fictional platform provider class
     *
     * @param string $name Provider name (e.g., 'platform_a', 'platform_b', 'platform_c')
     * @param string $className Fully qualified class name
     */
    public function registerProvider(string $name, string $className): void
    {
        if (!class_exists($className)) {
            throw new \InvalidArgumentException("Provider class {$className} does not exist");
        }

        if (!is_subclass_of($className, PlatformProviderInterface::class)) {
            throw new \InvalidArgumentException("Provider class {$className} must implement PlatformProviderInterface");
        }

        $this->providerClasses[$name] = $className;
    }

    /**
     * Get a fictional platform provider instance
     *
     * @param string $name Provider name
     * @return PlatformProviderInterface|null
     */
    public function getProvider(string $name): ?PlatformProviderInterface
    {
        // Return cached instance if available
        if (isset($this->providers[$name])) {
            return $this->providers[$name];
        }

        // Check if provider class is registered
        if (!isset($this->providerClasses[$name])) {
            return null;
        }

        // Get provider configuration
        $config = $this->getProviderConfig($name);
        if (!$config || !$config->is_active) {
            return null;
        }

        try {
            // Create provider instance with field mapping repository
            $className = $this->providerClasses[$name];
            $provider = new $className($config, $this->fieldMappingRepository);

            // Cache the instance
            $this->providers[$name] = $provider;

            return $provider;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get provider configuration from static configurations
     *
     * @param string $name
     * @return SocialPlatformProvider|null
     */
    public function getProviderConfig(string $name): ?SocialPlatformProvider
    {
        return $this->providerConfigs[$name] ?? null;
    }

    /**
     * Add a provider configuration
     *
     * @param SocialPlatformProvider $config
     */
    public function addProviderConfig(SocialPlatformProvider $config): void
    {
        $this->providerConfigs[$config->name] = $config;
    }

    /**
     * Check if a provider is registered
     *
     * @param string $name
     * @return bool
     */
    public function isProviderRegistered(string $name): bool
    {
        return isset($this->providerClasses[$name]);
    }

    /**
     * Load provider class mappings
     */
    protected function loadProviderClasses(): void
    {
        $this->registerProvider('platform_a', \App\Services\PlatformProviders\PlatformAProvider::class);
        $this->registerProvider('platform_b', \App\Services\PlatformProviders\PlatformBProvider::class);
        $this->registerProvider('platform_c', \App\Services\PlatformProviders\PlatformCProvider::class);
    }

    /**
     * Load default provider configurations
     */
    protected function loadDefaultConfigurations(): void
    {
        // Default Platform A configuration
        $this->providerConfigs['platform_a'] = new SocialPlatformProvider(
            name: 'platform_a',
            display_name: 'Platform A',
            is_active: true,
            supported_data_types: [
                'person_profile',
                'user_data',
                'profile_info',
            ],
        );

        // Default Platform B configuration
        $this->providerConfigs['platform_b'] = new SocialPlatformProvider(
            name: 'platform_b',
            display_name: 'Platform B',
            is_active: true,
            supported_data_types: [
                'person_profile',
                'user_info',
                'member_data',
            ],
        );

        // Default Platform C configuration
        $this->providerConfigs['platform_c'] = new SocialPlatformProvider(
            name: 'platform_c',
            display_name: 'Platform C',
            is_active: true,
            supported_data_types: [
                'person_profile',
                'individual_data',
                'contact_info',
            ],
        );
    }
}
