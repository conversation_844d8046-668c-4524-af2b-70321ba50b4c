<?php

namespace App\DTOs;

/**
 * UserProfileData DTO
 *
 * Data Transfer Object for standardized person profile information
 * from various fictional platforms. Contains only name, age, and email.
 */
class UserProfileData
{
    public function __construct(
        public string $provider,
        public string $name,
        public int $age,
        public string $email,
        public ?array $rawData = null,
    ) {}

    /**
     * Convert to database array format
     */
    public function toDatabaseArray(): array
    {
        return [
            'provider' => $this->provider,
            'name' => $this->name,
            'age' => $this->age,
            'email' => $this->email,
            'raw_provider_data' => $this->rawData,
        ];
    }

    /**
     * Check if email is valid
     */
    public function hasValidEmail(): bool
    {
        return filter_var($this->email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Get profile completeness score (0-100)
     */
    public function getCompletenessScore(): int
    {
        $score = 0;
        $maxScore = 3;

        // All fields are required for this simplified profile
        if (!empty($this->name)) $score++;
        if ($this->age > 0) $score++;
        if (!empty($this->email) && $this->hasValidEmail()) $score++;

        return (int) round(($score / $maxScore) * 100);
    }
}
