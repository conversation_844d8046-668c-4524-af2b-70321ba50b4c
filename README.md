# Social Platform Data Mapping Library

A simple PHP library demonstrating data mapping concepts using social media user profiles. This library transforms user profile data from different social platforms (GitHub, LinkedIn, Twitter) into a standardized format.

## Why This Example?

This social platform example is **much simpler** than payment processing while demonstrating the same core mapping concepts:

- **Easier to Understand**: User profiles vs complex financial data
- **Fewer Fields**: 5-8 profile fields vs 15+ payment fields  
- **No Complex Types**: No currencies, fees, transaction types, etc.
- **Relatable**: Everyone understands social media profiles
- **Same Patterns**: Still shows provider-specific → standardized mapping

## Features

- **Multi-Platform Support**: GitHub, LinkedIn, and Twitter providers
- **Data Mapping**: Transforms platform-specific user profiles into standardized format
- **Static Data Classes**: Simple PHP objects without database dependencies
- **Extensible**: Easy addition of new social platforms
- **Zero Dependencies**: No external libraries or database connections required

## Supported Social Platforms

- **GitHub**: User profiles with repositories, followers, and developer info
- **LinkedIn**: Professional profiles with connections and career details
- **Twitter**: Social profiles with tweets, followers, and engagement metrics

## Architecture

### Core Components

- **SocialPlatformProviderInterface**: Contract for all platform implementations
- **AbstractSocialPlatformProvider**: Base class with common functionality
- **SocialPlatformProviderRegistry**: Manages provider registration and instantiation
- **UserProfileData**: Standardized DTO for user profile events

### Data Structures

- **UserProfile**: Static data class for standardized user profile data
- **SocialPlatformProvider**: Static data class for provider configuration

## Usage

### Basic Data Mapping

```php
use App\Services\SocialPlatformProviderRegistry;
use App\Models\UserProfile;

// Initialize the provider registry
$registry = new SocialPlatformProviderRegistry();

// Get a provider (GitHub, LinkedIn, or Twitter)
$provider = $registry->getProvider('github');

// Parse user profile data into standardized format
$profileData = $provider->parseUserProfileData($userProfileJson, $headers);

// Map to standardized data format
$userProfileData = $provider->mapToStandardFormat($profileData);

// Create data object (no database required)
$userProfile = UserProfile::fromArray($userProfileData);
```

See `social_platform_example.php` for complete examples.

## Data Mapping Examples

### GitHub Profile Data

**Input (GitHub API format):**
```json
{
  "id": 12345,
  "login": "johndoe",
  "name": "John Doe",
  "email": "<EMAIL>",
  "bio": "Software developer",
  "avatar_url": "https://avatars.githubusercontent.com/u/12345",
  "followers": 150,
  "following": 75,
  "public_repos": 42
}
```

**Output (Standardized format):**
```php
UserProfile {
  provider: 'github',
  provider_user_id: '12345',
  username: 'johndoe',
  display_name: 'John Doe',
  email: '<EMAIL>',
  bio: 'Software developer',
  avatar_url: 'https://avatars.githubusercontent.com/u/12345',
  follower_count: 150,
  following_count: 75,
  metadata: ['public_repos' => 42]
}
```

### LinkedIn Profile Data

**Input (LinkedIn API format):**
```json
{
  "id": "abc123",
  "localizedFirstName": "Jane",
  "localizedLastName": "Smith",
  "headline": "Senior Product Manager",
  "numConnections": 500
}
```

**Output (Standardized format):**
```php
UserProfile {
  provider: 'linkedin',
  provider_user_id: 'abc123',
  username: 'abc123',
  display_name: 'Jane Smith',
  bio: 'Senior Product Manager',
  metadata: ['num_connections' => 500]
}
```

## Provider Configuration

Create provider configurations as static data objects:

```php
use App\Models\SocialPlatformProvider;
use App\Services\SocialPlatformProviderRegistry;

// Initialize registry (comes with default GitHub, LinkedIn, Twitter configs)
$registry = new SocialPlatformProviderRegistry();

// Or add custom provider configuration
$customConfig = new SocialPlatformProvider(
    name: 'github',
    display_name: 'GitHub',
    is_active: true,
    supported_data_types: [
        'user_profile',
        'public_profile',
        'developer_profile',
    ],
);

$registry->addProviderConfig($customConfig);
```

## Extending the System

### Adding New Social Platforms

1. **Create Provider Class**:
```php
class InstagramProvider extends AbstractSocialPlatformProvider
{
    protected array $supportedDataTypes = ['user_profile', 'media_profile'];
    
    public function parseUserProfileData(string $payload, array $headers = []): UserProfileData
    {
        // Parse Instagram-specific data format
    }
    
    public function mapToStandardFormat(UserProfileData $profileData): array
    {
        return $profileData->toDatabaseArray();
    }
}
```

2. **Register Provider**:
```php
$registry->registerProvider('instagram', InstagramProvider::class);
```

## Standardized Fields

The system maps all platform-specific data to these standardized fields:

- **provider**: Platform name (github, linkedin, twitter)
- **provider_user_id**: Platform's unique user identifier
- **username**: Platform username/handle
- **display_name**: User's display name or full name
- **email**: Email address (if available)
- **bio**: User bio/description/headline
- **avatar_url**: Profile picture URL
- **profile_url**: Link to user's profile page
- **follower_count**: Number of followers
- **following_count**: Number of people following
- **metadata**: Platform-specific additional data

## Testing

Run the test suite to verify functionality:

```bash
php test_social_platform_mapping.php
```

The test demonstrates:
- ✅ UserProfile data class functionality
- ✅ SocialPlatformProvider configuration management
- ✅ SocialPlatformProviderRegistry with multiple platforms
- ✅ End-to-end profile data mapping (GitHub example)
- ✅ Data persistence simulation without dependencies

## Benefits Over Payment Example

1. **Simpler Data**: User profiles are easier to understand than financial transactions
2. **Fewer Fields**: Only 8-10 fields vs 15+ payment fields
3. **No Complex Types**: No currencies, amounts, fees, or transaction states
4. **Relatable**: Everyone understands social media profiles
5. **Clear Mapping**: Obvious how different platforms represent the same concepts
6. **Easy to Extend**: Simple to add new social platforms

## Real-World Use Cases

This pattern is useful for:
- **User Authentication**: Standardizing OAuth profile data
- **Social Login**: Mapping profiles from different providers
- **User Onboarding**: Importing profile data during registration
- **Profile Aggregation**: Combining profiles from multiple platforms
- **Data Migration**: Moving user data between systems

## License

This project is licensed under the MIT License.
