<?php

namespace Tests\Feature;

use App\Models\IntegrationField;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * PHPUnit test suite for IntegrationField model and database functionality
 */
class IntegrationFieldTest extends TestCase
{
    use RefreshDatabase;

    private array $sampleMapping;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sampleMapping = [
            'provider_name' => 'platform_a',
            'local_field_name' => 'name',
            'provider_field_name' => 'full_name',
            'field_type' => 'string',
            'is_required' => true,
            'is_active' => true,
            'description' => 'Maps Platform A full_name to standardized name field',
        ];
    }

    #[Test]
    public function integration_field_can_create_an_integration_field_mapping(): void
    {
        $mapping = IntegrationField::create($this->sampleMapping);

        $this->assertInstanceOf(IntegrationField::class, $mapping);
        $this->assertEquals('platform_a', $mapping->provider_name);
        $this->assertEquals('name', $mapping->local_field_name);
        $this->assertEquals('full_name', $mapping->provider_field_name);
        $this->assertTrue($mapping->is_required);
        $this->assertTrue($mapping->is_active);
    }

    #[Test]
    public function integration_field_can_retrieve_integration_field_mappings(): void
    {
        IntegrationField::create($this->sampleMapping);

        $retrieved = IntegrationField::first();

        $this->assertEquals('platform_a', $retrieved->provider_name);
        $this->assertEquals('name', $retrieved->local_field_name);
        $this->assertEquals('full_name', $retrieved->provider_field_name);
    }

    #[Test]
    public function integration_field_casts_boolean_fields_correctly(): void
    {
        $mapping = IntegrationField::create($this->sampleMapping);

        $this->assertTrue($mapping->is_required);
        $this->assertTrue($mapping->is_active);
    }

    #[Test]
    public function integration_field_handles_metadata_as_json(): void
    {
        $mappingWithMetadata = array_merge($this->sampleMapping, [
            'metadata' => ['priority' => 'high', 'category' => 'core']
        ]);

        $mapping = IntegrationField::create($mappingWithMetadata);

        $this->assertIsArray($mapping->metadata);
        $this->assertEquals('high', $mapping->metadata['priority']);
        $this->assertEquals('core', $mapping->metadata['category']);
    }

    private function createQueryBuilderTestData(): void
    {
        // Create test data
        IntegrationField::create([
            'provider_name' => 'platform_a',
            'local_field_name' => 'name',
            'provider_field_name' => 'full_name',
            'is_active' => true,
            'is_required' => true,
        ]);

        IntegrationField::create([
            'provider_name' => 'platform_a',
            'local_field_name' => 'age',
            'provider_field_name' => 'user_age',
            'is_active' => true,
            'is_required' => false,
        ]);

        IntegrationField::create([
            'provider_name' => 'platform_b',
            'local_field_name' => 'name',
            'provider_field_name' => 'display_name',
            'is_active' => false,
            'is_required' => true,
        ]);
    }

    #[Test]
    public function integration_field_can_filter_by_provider_using_where_clause(): void
    {
        $this->createQueryBuilderTestData();

        $platformAMappings = IntegrationField::where('provider_name', 'platform_a')->get();

        $this->assertCount(2, $platformAMappings);
        $this->assertCount(1, $platformAMappings->pluck('provider_name')->unique());
        $this->assertEquals('platform_a', $platformAMappings->first()->provider_name);
    }

    #[Test]
    public function integration_field_can_filter_by_local_field_using_where_clause(): void
    {
        $this->createQueryBuilderTestData();

        $nameMappings = IntegrationField::where('local_field_name', 'name')->get();

        $this->assertCount(2, $nameMappings);
        $this->assertCount(1, $nameMappings->pluck('local_field_name')->unique());
        $this->assertEquals('name', $nameMappings->first()->local_field_name);
    }

    #[Test]
    public function integration_field_can_filter_active_mappings_using_where_clause(): void
    {
        $this->createQueryBuilderTestData();

        $activeMappings = IntegrationField::where('is_active', true)->get();

        $this->assertCount(2, $activeMappings);
        $this->assertTrue($activeMappings->every(fn($mapping) => $mapping->is_active));
    }

    #[Test]
    public function integration_field_can_filter_required_mappings_using_where_clause(): void
    {
        $this->createQueryBuilderTestData();

        $requiredMappings = IntegrationField::where('is_required', true)->get();

        $this->assertCount(2, $requiredMappings);
        $this->assertTrue($requiredMappings->every(fn($mapping) => $mapping->is_required));
    }

    #[Test]
    public function integration_field_can_combine_where_clauses(): void
    {
        $this->createQueryBuilderTestData();

        $platformAActiveMappings = IntegrationField::where('provider_name', 'platform_a')->where('is_active', true)->get();

        $this->assertCount(2, $platformAActiveMappings);
        $this->assertTrue($platformAActiveMappings->every(fn($mapping) =>
            $mapping->provider_name === 'platform_a' && $mapping->is_active
        ));
    }

    private function createStaticHelperTestData(): void
    {
        IntegrationField::create([
            'provider_name' => 'platform_a',
            'local_field_name' => 'name',
            'provider_field_name' => 'full_name',
            'is_active' => true,
        ]);

        IntegrationField::create([
            'provider_name' => 'platform_a',
            'local_field_name' => 'age',
            'provider_field_name' => 'user_age',
            'is_active' => true,
        ]);
    }

    #[Test]
    public function integration_field_can_get_specific_mapping_using_get_mapping_method(): void
    {
        $this->createStaticHelperTestData();

        $mapping = IntegrationField::query()->where('provider_name', 'platform_a')
            ->where('local_field_name', 'name')
            ->where('is_active', true)
            ->first();

        $this->assertNotNull($mapping);
        $this->assertEquals('platform_a', $mapping->provider_name);
        $this->assertEquals('name', $mapping->local_field_name);
        $this->assertEquals('full_name', $mapping->provider_field_name);
    }

    #[Test]
    public function integration_field_returns_null_for_non_existent_mapping(): void
    {
        $this->createStaticHelperTestData();

        $mapping = IntegrationField::query()->where('provider_name', 'non_existent')
            ->where('local_field_name', 'name')
            ->where('is_active', true)
            ->first();

        $this->assertNull($mapping);
    }

    #[Test]
    public function integration_field_can_get_all_mappings_for_provider_using_get_mappings_for_provider_method(): void
    {
        $this->createStaticHelperTestData();

        /** @var \Illuminate\Support\Collection<IntegrationField> $mappings */
        $mappings = IntegrationField::query()
            ->where('provider_name', 'platform_a')
            ->where('is_active', true)
            ->orderBy('local_field_name')
            ->get();

        $this->assertCount(2, $mappings);
        $this->assertCount(1, $mappings->pluck('provider_name')->unique());
        $this->assertEquals('platform_a', $mappings->first()->provider_name);
    }

    #[Test]
    public function integration_field_enforces_unique_constraint_on_provider_name_and_local_field_name(): void
    {
        IntegrationField::create($this->sampleMapping);

        $this->expectException(\Illuminate\Database\QueryException::class);
        IntegrationField::create($this->sampleMapping);
    }
}
