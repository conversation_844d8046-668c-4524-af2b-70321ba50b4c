<?php

namespace Tests\Feature;

use App\Contracts\PlatformProviderInterface;
use App\Models\UserProfile;
use App\Models\SocialPlatformProvider;
use App\Services\PlatformProviderRegistry;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * PHPUnit test suite for fictional platform data mapping system
 */
class PlatformMappingTest extends TestCase
{
    private array $sampleProfileData;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sampleProfileData = [
            'provider' => 'platform_a',
            'name' => '<PERSON> Doe',
            'age' => 30,
            'email' => '<EMAIL>',
        ];
    }

    #[Test]
    public function user_profile_can_be_created_from_array_data(): void
    {
        $userProfile = UserProfile::fromArray($this->sampleProfileData);

        $this->assertEquals('John Doe', $userProfile->name);
        $this->assertEquals(30, $userProfile->age);
        $this->assertEquals('<EMAIL>', $userProfile->email);
        $this->assertEquals('platform_a', $userProfile->provider);
    }

    #[Test]
    public function user_profile_correctly_validates_email_addresses(): void
    {
        $userProfile = UserProfile::fromArray($this->sampleProfileData);

        $this->assertTrue($userProfile->hasValidEmail());

        // Test with invalid email
        $dataWithInvalidEmail = $this->sampleProfileData;
        $dataWithInvalidEmail['email'] = 'invalid-email';
        $profileWithInvalidEmail = UserProfile::fromArray($dataWithInvalidEmail);

        $this->assertFalse($profileWithInvalidEmail->hasValidEmail());
    }

    #[Test]
    public function user_profile_correctly_identifies_adult_status(): void
    {
        $userProfile = UserProfile::fromArray($this->sampleProfileData);

        $this->assertTrue($userProfile->isAdult()); // 30 years old

        // Test with minor
        $minorData = $this->sampleProfileData;
        $minorData['age'] = 16;
        $minorProfile = UserProfile::fromArray($minorData);

        $this->assertFalse($minorProfile->isAdult());
    }

    #[Test]
    public function user_profile_categorizes_age_correctly(): void
    {
        $userProfile = UserProfile::fromArray($this->sampleProfileData);

        $this->assertEquals('adult', $userProfile->getAgeCategory()); // 30 years old

        // Test minor
        $minorData = $this->sampleProfileData;
        $minorData['age'] = 16;
        $minorProfile = UserProfile::fromArray($minorData);
        $this->assertEquals('minor', $minorProfile->getAgeCategory());

        // Test senior
        $seniorData = $this->sampleProfileData;
        $seniorData['age'] = 70;
        $seniorProfile = UserProfile::fromArray($seniorData);
        $this->assertEquals('senior', $seniorProfile->getAgeCategory());
    }

    #[Test]
    public function user_profile_can_be_converted_to_array(): void
    {
        $userProfile = UserProfile::fromArray($this->sampleProfileData);
        $array = $userProfile->toArray();

        $this->assertIsArray($array);
        $this->assertArrayHasKey('name', $array);
        $this->assertArrayHasKey('age', $array);
        $this->assertArrayHasKey('email', $array);
        $this->assertArrayHasKey('provider', $array);
        $this->assertGreaterThan(0, count($array));
    }

    #[Test]
    public function user_profile_generates_display_summary_correctly(): void
    {
        $userProfile = UserProfile::fromArray($this->sampleProfileData);

        $this->assertEquals('John Doe (30 years old) - <EMAIL>', $userProfile->getDisplaySummary());
    }

    #[Test]
    public function social_platform_provider_can_be_created_with_configuration(): void
    {
        $provider = new SocialPlatformProvider(
            name: 'platform_a',
            display_name: 'Platform A',
            is_active: true,
            supported_data_types: ['person_profile', 'user_data'],
            provider_settings: ['api_version' => 'v1'],
        );

        $this->assertEquals('platform_a', $provider->name);
        $this->assertEquals('Platform A', $provider->display_name);
        $this->assertTrue($provider->is_active);
    }

    #[Test]
    public function social_platform_provider_correctly_reports_active_status(): void
    {
        $activeProvider = new SocialPlatformProvider(
            name: 'platform_a',
            display_name: 'Platform A',
            is_active: true,
        );

        $inactiveProvider = new SocialPlatformProvider(
            name: 'platform_a',
            display_name: 'Platform A',
            is_active: false,
        );

        $this->assertTrue($activeProvider->isActive());
        $this->assertFalse($inactiveProvider->isActive());
    }

    #[Test]
    public function social_platform_provider_correctly_identifies_supported_data_types(): void
    {
        $provider = new SocialPlatformProvider(
            name: 'platform_a',
            display_name: 'Platform A',
            supported_data_types: ['person_profile', 'user_data'],
        );

        $this->assertTrue($provider->supportsDataType('person_profile'));
        $this->assertTrue($provider->supportsDataType('user_data'));
        $this->assertFalse($provider->supportsDataType('private_profile'));
    }

    #[Test]
    public function social_platform_provider_can_get_and_set_provider_settings(): void
    {
        $provider = new SocialPlatformProvider(
            name: 'platform_a',
            display_name: 'Platform A',
            provider_settings: ['api_version' => 'v1'],
        );

        $this->assertEquals('v1', $provider->getSetting('api_version'));
        $this->assertEquals('default', $provider->getSetting('non_existent', 'default'));

        $provider->setSetting('new_setting', 'new_value');
        $this->assertEquals('new_value', $provider->getSetting('new_setting'));
    }

    private PlatformProviderRegistry $registry;

    private function setUpPlatformProviderRegistry(): void
    {
        $this->registry = new PlatformProviderRegistry();
    }

    #[Test]
    public function platform_provider_registry_can_load_platform_aprovider(): void
    {
        $this->setUpPlatformProviderRegistry();

        $platformAProvider = $this->registry->getProvider('platform_a');

        $this->assertNotNull($platformAProvider);
        $this->assertEquals('Platform A', $platformAProvider->getDisplayName());
        $this->assertEquals('platform_a', $platformAProvider->getName());
    }

    #[Test]
    public function platform_provider_registry_can_load_platform_bprovider(): void
    {
        $this->setUpPlatformProviderRegistry();

        $platformBProvider = $this->registry->getProvider('platform_b');

        $this->assertNotNull($platformBProvider);
        $this->assertEquals('Platform B', $platformBProvider->getDisplayName());
        $this->assertEquals('platform_b', $platformBProvider->getName());
    }

    #[Test]
    public function platform_provider_registry_can_load_platform_cprovider(): void
    {
        $this->setUpPlatformProviderRegistry();

        $platformCProvider = $this->registry->getProvider('platform_c');

        $this->assertNotNull($platformCProvider);
        $this->assertEquals('Platform C', $platformCProvider->getDisplayName());
        $this->assertEquals('platform_c', $platformCProvider->getName());
    }

    #[Test]
    public function platform_provider_registry_returns_null_for_non_existent_provider(): void
    {
        $this->setUpPlatformProviderRegistry();

        $nonExistentProvider = $this->registry->getProvider('non_existent');

        $this->assertNull($nonExistentProvider);
    }

    #[Test]
    public function platform_provider_registry_can_check_if_provider_is_registered(): void
    {
        $this->setUpPlatformProviderRegistry();

        $this->assertTrue($this->registry->isProviderRegistered('platform_a'));
        $this->assertTrue($this->registry->isProviderRegistered('platform_b'));
        $this->assertTrue($this->registry->isProviderRegistered('platform_c'));
        $this->assertFalse($this->registry->isProviderRegistered('non_existent'));
    }

    #[Test]
    public function platform_provider_registry_can_add_custom_provider_configuration(): void
    {
        $this->setUpPlatformProviderRegistry();

        $customConfig = new SocialPlatformProvider(
            name: 'custom',
            display_name: 'Custom Platform',
            is_active: true,
        );

        $this->registry->addProviderConfig($customConfig);
        $retrievedConfig = $this->registry->getProviderConfig('custom');

        $this->assertNotNull($retrievedConfig);
        $this->assertEquals('custom', $retrievedConfig->name);
        $this->assertEquals('Custom Platform', $retrievedConfig->display_name);
    }

    private string $platformAPayload;

    private function setUpEndToEndDataMapping(): void
    {
        $this->registry = new PlatformProviderRegistry();
        $this->platformAPayload = json_encode([
            'full_name' => 'Test User',
            'user_age' => 25,
            'email_address' => '<EMAIL>',
            'account_id' => 'user_12345',
            'registration_date' => '2020-01-01',
        ]);
    }

    #[Test]
    public function end_to_end_data_mapping_can_parse_platform_a_person_profile_data(): void
    {
        $this->setUpEndToEndDataMapping();

        $platformAProvider = $this->registry->getProvider('platform_a');

        $this->assertNotNull($platformAProvider);

        $profileData = $platformAProvider->parsePersonProfileData($this->platformAPayload, []);

        $this->assertEquals('Test User', $profileData->name);
        $this->assertEquals(25, $profileData->age);
        $this->assertEquals('<EMAIL>', $profileData->email);
        $this->assertEquals('platform_a', $profileData->provider);
    }

    #[Test]
    public function end_to_end_data_mapping_can_map_profile_data_to_standardized_format(): void
    {
        $this->setUpEndToEndDataMapping();

        /** @var PlatformProviderInterface $platformAProvider */
        $platformAProvider = $this->registry->getProvider('platform_a');
        $profileData = $platformAProvider->parsePersonProfileData($this->platformAPayload, []);

        $standardData = $platformAProvider->mapToStandardFormat($profileData);

        $this->assertIsArray($standardData);
        $this->assertArrayHasKey('provider', $standardData);
        $this->assertArrayHasKey('name', $standardData);
        $this->assertArrayHasKey('age', $standardData);
        $this->assertArrayHasKey('email', $standardData);
        $this->assertEquals('platform_a', $standardData['provider']);
        $this->assertEquals('Test User', $standardData['name']);
    }

    #[Test]
    public function end_to_end_data_mapping_can_create_user_profile_object_from_mapped_data(): void
    {
        $this->setUpEndToEndDataMapping();

        /** @var PlatformProviderInterface $platformAProvider */
        $platformAProvider = $this->registry->getProvider('platform_a');
        $profileData = $platformAProvider->parsePersonProfileData($this->platformAPayload, []);
        $standardData = $platformAProvider->mapToStandardFormat($profileData);

        $userProfile = UserProfile::fromArray($standardData);

        $this->assertEquals('Test User', $userProfile->name);
        $this->assertEquals(25, $userProfile->age);
        $this->assertEquals('<EMAIL>', $userProfile->email);
        $this->assertEquals('platform_a', $userProfile->provider);
    }

    #[Test]
    public function end_to_end_data_mapping_calculates_completeness_score_correctly(): void
    {
        $this->setUpEndToEndDataMapping();

        $platformAProvider = $this->registry->getProvider('platform_a');
        $profileData = $platformAProvider->parsePersonProfileData($this->platformAPayload, []);

        $completenessScore = $profileData->getCompletenessScore();

        $this->assertIsInt($completenessScore);
        $this->assertEquals(100, $completenessScore); // All required fields present
    }

    #[Test]
    public function data_persistence_simulation_can_serialize_and_deserialize_user_profile_objects(): void
    {
        $originalProfile = UserProfile::fromArray($this->sampleProfileData);

        // Simulate saving to data store
        $dataStore = [];
        $profileArray = $originalProfile->toArray();
        $dataStore['user_profiles'][] = $profileArray;

        $this->assertCount(1, $dataStore['user_profiles']);

        // Simulate retrieving and recreating objects
        $retrievedProfileData = $dataStore['user_profiles'][0];
        $retrievedProfile = UserProfile::fromArray($retrievedProfileData);

        $this->assertEquals($originalProfile->name, $retrievedProfile->name);
        $this->assertEquals($originalProfile->age, $retrievedProfile->age);
        $this->assertEquals($originalProfile->email, $retrievedProfile->email);
        $this->assertEquals($originalProfile->provider, $retrievedProfile->provider);
    }

    #[Test]
    public function data_persistence_simulation_maintains_data_integrity_through_serialization_cycle(): void
    {
        $originalProfile = UserProfile::fromArray($this->sampleProfileData);

        // Convert to array and back
        $serialized = $originalProfile->toArray();
        $deserialized = UserProfile::fromArray($serialized);

        // Check all important fields are preserved
        $this->assertEquals($originalProfile->provider, $deserialized->provider);
        $this->assertEquals($originalProfile->name, $deserialized->name);
        $this->assertEquals($originalProfile->age, $deserialized->age);
        $this->assertEquals($originalProfile->email, $deserialized->email);
        $this->assertEquals($originalProfile->raw_provider_data, $deserialized->raw_provider_data);
    }

    #[Test]
    public function data_persistence_simulation_can_handle_multiple_profiles_in_data_store(): void
    {
        $profile1 = UserProfile::fromArray($this->sampleProfileData);

        $profile2Data = $this->sampleProfileData;
        $profile2Data['name'] = 'Jane Doe';
        $profile2Data['age'] = 28;
        $profile2Data['email'] = '<EMAIL>';
        $profile2 = UserProfile::fromArray($profile2Data);

        $dataStore = [];
        $dataStore['user_profiles'][] = $profile1->toArray();
        $dataStore['user_profiles'][] = $profile2->toArray();

        $this->assertCount(2, $dataStore['user_profiles']);

        $retrieved1 = UserProfile::fromArray($dataStore['user_profiles'][0]);
        $retrieved2 = UserProfile::fromArray($dataStore['user_profiles'][1]);

        $this->assertEquals('John Doe', $retrieved1->name);
        $this->assertEquals('Jane Doe', $retrieved2->name);
    }
}
