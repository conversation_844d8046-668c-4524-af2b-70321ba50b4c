<?php

namespace Tests\Feature;

use App\Models\FieldMapping;
use App\Services\FieldMappingRepository;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * PHPUnit test suite for field mapping functionality
 */
class FieldMappingTest extends TestCase
{
    #[Test]
    public function field_mapping_can_be_created_with_mapping_configuration(): void
    {
        $mapping = new FieldMapping(
            provider_name: 'platform_a',
            local_field_name: 'name',
            provider_field_name: 'full_name'
        );

        $this->assertEquals('platform_a', $mapping->provider_name);
        $this->assertEquals('name', $mapping->local_field_name);
        $this->assertEquals('full_name', $mapping->provider_field_name);
    }

    #[Test]
    public function field_mapping_can_check_if_mapping_is_for_specific_provider(): void
    {
        $mapping = new FieldMapping(
            provider_name: 'platform_a',
            local_field_name: 'name',
            provider_field_name: 'full_name'
        );

        $this->assertTrue($mapping->isForProvider('platform_a'));
        $this->assertFalse($mapping->isForProvider('platform_b'));
    }

    #[Test]
    public function field_mapping_can_check_if_mapping_is_for_specific_local_field(): void
    {
        $mapping = new FieldMapping(
            provider_name: 'platform_a',
            local_field_name: 'name',
            provider_field_name: 'full_name'
        );

        $this->assertTrue($mapping->isForLocalField('name'));
        $this->assertFalse($mapping->isForLocalField('age'));
    }

    #[Test]
    public function field_mapping_can_be_converted_to_and_from_array(): void
    {
        $mapping = new FieldMapping(
            provider_name: 'platform_a',
            local_field_name: 'name',
            provider_field_name: 'full_name'
        );
        $arrayData = $mapping->toArray();

        $this->assertEquals('platform_a', $arrayData['provider_name']);
        $this->assertEquals('name', $arrayData['local_field_name']);
        $this->assertEquals('full_name', $arrayData['provider_field_name']);
    }

    private FieldMappingRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new FieldMappingRepository();
    }

    #[Test]
    public function field_mapping_repository_loads_default_mappings_for_all_platforms(): void
    {
        $allMappings = $this->repository->getAllMappings();

        $this->assertCount(9, $allMappings); // 3 platforms × 3 fields each
    }

    #[Test]
    public function field_mapping_repository_can_get_mappings_for_platform_a(): void
    {
        $platformAMappings = $this->repository->getMappingsForProvider('platform_a');

        $this->assertArrayHasKey('name', $platformAMappings);
        $this->assertArrayHasKey('age', $platformAMappings);
        $this->assertArrayHasKey('email', $platformAMappings);
        $this->assertEquals('full_name', $platformAMappings['name']);
        $this->assertEquals('user_age', $platformAMappings['age']);
        $this->assertEquals('email_address', $platformAMappings['email']);
    }

    #[Test]
    public function field_mapping_repository_can_get_mappings_for_platform_b(): void
    {
        $platformBMappings = $this->repository->getMappingsForProvider('platform_b');

        $this->assertArrayHasKey('name', $platformBMappings);
        $this->assertArrayHasKey('age', $platformBMappings);
        $this->assertArrayHasKey('email', $platformBMappings);
        $this->assertEquals('display_name', $platformBMappings['name']);
        $this->assertEquals('years_old', $platformBMappings['age']);
        $this->assertEquals('contact_email', $platformBMappings['email']);
    }

    #[Test]
    public function field_mapping_repository_can_get_mappings_for_platform_c(): void
    {
        $platformCMappings = $this->repository->getMappingsForProvider('platform_c');

        $this->assertArrayHasKey('name', $platformCMappings);
        $this->assertArrayHasKey('age', $platformCMappings);
        $this->assertArrayHasKey('email', $platformCMappings);
        $this->assertEquals('person_name', $platformCMappings['name']);
        $this->assertEquals('age_in_years', $platformCMappings['age']);
        $this->assertEquals('email_addr', $platformCMappings['email']);
    }

    #[Test]
    public function field_mapping_repository_can_get_specific_provider_field_name_for_local_field(): void
    {
        $this->assertEquals('full_name', $this->repository->getProviderFieldName('platform_a', 'name'));
        $this->assertEquals('display_name', $this->repository->getProviderFieldName('platform_b', 'name'));
        $this->assertEquals('person_name', $this->repository->getProviderFieldName('platform_c', 'name'));

        $this->assertEquals('user_age', $this->repository->getProviderFieldName('platform_a', 'age'));
        $this->assertEquals('years_old', $this->repository->getProviderFieldName('platform_b', 'age'));
        $this->assertEquals('age_in_years', $this->repository->getProviderFieldName('platform_c', 'age'));
    }

    #[Test]
    public function field_mapping_repository_returns_null_for_non_existent_provider_or_field(): void
    {
        $this->assertNull($this->repository->getProviderFieldName('non_existent', 'name'));
        $this->assertNull($this->repository->getProviderFieldName('platform_a', 'non_existent'));
    }

    #[Test]
    public function field_mapping_repository_can_add_new_field_mappings(): void
    {
        $newMapping = new FieldMapping(
            provider_name: 'platform_d',
            local_field_name: 'name',
            provider_field_name: 'user_name'
        );

        $this->repository->addMapping($newMapping);

        $this->assertEquals('user_name', $this->repository->getProviderFieldName('platform_d', 'name'));
    }

    #[Test]
    public function field_mapping_repository_demonstrates_field_mapping_flexibility(): void
    {
        // All platforms map different field names to the same local field
        $nameFieldMappings = [
            'platform_a' => $this->repository->getProviderFieldName('platform_a', 'name'),
            'platform_b' => $this->repository->getProviderFieldName('platform_b', 'name'),
            'platform_c' => $this->repository->getProviderFieldName('platform_c', 'name'),
        ];

        $this->assertEquals('full_name', $nameFieldMappings['platform_a']);
        $this->assertEquals('display_name', $nameFieldMappings['platform_b']);
        $this->assertEquals('person_name', $nameFieldMappings['platform_c']);

        // All different provider field names map to the same local field 'name'
        $this->assertCount(3, array_unique($nameFieldMappings)); // All different
    }
}
